module ApplicationHelper
  def add_breadcrumb(name, path)
    @breadcrumbs ||= []
    @breadcrumbs << { name: name, path: path }
  end

  def render_breadcrumbs
    return unless @breadcrumbs&.any?

    content_tag :nav, class: "breadcrumbs text-sm" do
      content_tag :ul do
        @breadcrumbs.map do |crumb|
          content_tag :li do
            if crumb[:path]
              link_to crumb[:name], crumb[:path]
            else
              crumb[:name]
            end
          end
        end.join.html_safe
      end
    end
  end
end
