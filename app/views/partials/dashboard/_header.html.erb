<div class="bg-base-100/90 text-base-content sticky top-0 z-30">
    <nav class="navbar w-full py-0">
        <div class="flex-1">
            <%= render_breadcrumbs %>
        </div>
        <div class="flex xl:gap-1">            
            <%= render 'partials/language_switch' %>
            <%= render 'partials/theme_controller', light_theme: @user_light_theme , dark_theme: @user_dark_theme %>
            <div class="dropdown dropdown-end">            
            <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                <div class="w-10 rounded-full">
                <%= image_tag current_user.avatar_thumbnail %>
                </div>
            </div>
            <ul
                tabindex="0"
                class="menu menu-sm dropdown-content bg-base-100 rounded-box z-1 mt-3 w-52 p-2 shadow">                   
                <% if user_signed_in? %>                
                    <li><%= link_to "Profile", edit_user_registration_path %></li>
                    <li><%= link_to "Logout", destroy_user_session_path, data: { turbo_method: :delete } %></li>
                <% end %>
            </ul>
            </div>
        </div>
    </nav>
</div>