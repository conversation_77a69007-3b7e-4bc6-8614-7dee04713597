<!DOCTYPE html>
<html lang="en">
  <head>
    <title><%= content_for?(:title) ? yield(:title) : "RORSCHOOLS" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= display_meta_tags(site: "RORSCHOOLS") %>
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "application", "data-turbo-track": "reload", type: "module" %>    
    <script>      
      (function() {
        const theme = localStorage.getItem('theme');
        if (theme == 'silk') {
          document.documentElement.setAttribute('data-theme', 'winter');
          return;
        }
        const lightTheme = "<%= get_user_option(current_user,"light_theme", "winter") %>";
        const darkTheme = "<%= get_user_option(current_user,"dark_theme", "dark") %>";

        const effectiveTheme = theme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? darkTheme : lightTheme);
        document.documentElement.setAttribute('data-theme', effectiveTheme);
      })();
    </script>
  </head>
  <body>
    <main class="drawer lg:drawer-open">
      <input id="drawer-sidebar" type="checkbox" class="drawer-toggle" />
      <div class="drawer-content p-2">  
        <%= render 'partials/dashboard/header' %>
        <%= render 'partials/alerts' %>      
        <%= yield %>        
        <%= render 'partials/dashboard/dock' %>
      </div>
      <%= render 'partials/dashboard/sidebar' %>      
    </main>    
  </body>
</html>
