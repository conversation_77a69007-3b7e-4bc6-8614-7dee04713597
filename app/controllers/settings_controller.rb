class SettingsController < ApplicationController
  layout "dashboard"
  before_action :authorize_settings
  before_action :set_settings_breadcrumbs
  def index
    @site_name = get_option("site_name", "Rorschools")
    @site_description = get_option("site_description", "A school website")
    @site_light_theme = get_option("site_light_theme", "winter")
    @site_dark_theme = get_option("site_dark_theme", "black")
    @user_light_theme = get_user_option(current_user, "light_theme", "winter")
    @user_dark_theme = get_user_option(current_user, "dark_theme", "black")

    @light_theme_list = [
      "light", "cupcake", "bumblebee", "emerald", "corporate", "retro", "cyberpunk", "valentine", "garden", "aqua", "lofi", "pastel", "fantasy", "wireframe", "cmyk", "autumn", "acid", "lemonade", "winter", "nord", "caramellatte", "silk"
    ]
    @dark_theme_list = [
      "dark", "synthwave", "halloween", "forest", "black", "luxury", "dracula", "business", "night", "coffee", "dim", "sunset", "abyss"
    ]
  end

  def update
    setting_params.each do |key, value|
      if key.starts_with?("site_")
        update_option(key, value)
      else
        update_user_option(current_user, key, value)
      end
    end
    redirect_to dashboard_settings_path, notice: "Settings updated successfully!"
  end

  private

  def authorize_settings
    authorize! :update, :settings
  end

  def setting_params
    params.require(:setting).permit(:site_name, :site_description, :site_light_theme, :site_dark_theme, :light_theme, :dark_theme)
  end

  def set_settings_breadcrumbs
    helpers.add_breadcrumb "Dashboard", dashboard_root_path
    helpers.add_breadcrumb "Settings", dashboard_settings_path
  end
end
