class Users::RegistrationsController < Devise::RegistrationsController
  before_action :set_users_breadcrumb, only: [ :edit ]
  # GET /resource/edit
  def edit
    super
  end

  # PUT /resource
  def update
    self.resource = resource_class.to_adapter.get!(send(:"current_#{resource_name}").to_key)
    prev_unconfirmed_email = resource.unconfirmed_email if resource.respond_to?(:unconfirmed_email)

    Rails.logger.info "Avatar params: #{account_update_params[:avatar].inspect}"
    Rails.logger.info "All params: #{account_update_params.inspect}"

    resource_updated = update_resource(resource, account_update_params)
    yield resource if block_given?

    if resource_updated
      if account_update_params[:avatar].present?
        flash[:notice] = "Profile updated successfully! Your avatar has been changed."
      else
        set_flash_message_for_update(resource, prev_unconfirmed_email)
      end
      bypass_sign_in resource, scope: resource_name if sign_in_after_change_password?

      respond_with resource, location: after_update_path_for(resource)
    else
      Rails.logger.error "Update failed. Errors: #{resource.errors.full_messages}"
      clean_up_passwords resource
      set_minimum_password_length
      respond_with resource
    end
  end

  protected

  # The path used after a profile update.
  def after_update_path_for(resource)
    if resource.viewer?
      root_path(locale: I18n.locale)
    else
      dashboard_root_path(locale: I18n.locale)
    end
  end

  # The path used after sign up.
  def after_sign_up_path_for(resource)
    super(resource)
  end

  # The path used after sign up for inactive accounts.
  def after_inactive_sign_up_path_for(resource)
    super(resource)
  end

  def update_resource(resource, params)
    # Require current password if user is trying to change password
    if params[:password].blank? && params[:password_confirmation].blank?
      params.delete(:password)
      params.delete(:password_confirmation)
      params.delete(:current_password)
      resource.update_without_password(params)
    else
      resource.update_with_password(params)
    end
  end

  def set_flash_message_for_update(resource, prev_unconfirmed_email)
    return unless is_flashing_format?

    flash_key = if update_needs_confirmation?(resource, prev_unconfirmed_email)
                  :update_needs_confirmation
    elsif sign_in_after_change_password?
                  :updated
    else
                  :updated_but_not_signed_in
    end
    set_flash_message :notice, flash_key
  end

  def set_users_breadcrumb
    helpers.add_breadcrumb "Users", edit_user_registration_path
  end
end
